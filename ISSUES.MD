1.success approve modal have a gap no fully covered the backdrop and have this error 

Database query error: {
  code: 'ER_BAD_FIELD_ERROR',
  message: "Unknown column 'id' in 'where clause'",
  sql: '\n' +
    '      SELECT\n' +
    '        application_status,\n' +
    '        created_at,\n' +
    '        updated_at,\n' +
    '        verificati...',
  params: [ 5 ]
}
2. 
Database query error: {
  code: 'ER_BAD_FIELD_ERROR',
  message: "Unknown column 'target_table' in 'field list'",
  sql: '\n' +
    '          INSERT INTO admin_logs (action, target_table, target_id, admin_id, details)\n' +
    '          VAL...',
  params: [
    'restrict_business',
    'service_providers',
    5,
    'admin',
    '{"action":"restrict","businessId":5,"newStatus":"restricted","newApplicationStatus":"restricted"}'
  ]
}
Database query error: {
  code: 'ER_BAD_FIELD_ERROR',
  message: "Unknown column 'updated_at' in 'field list'",
  sql: '\n' +
    '                UPDATE user_restrictions\n' +
    '                SET is_active = 0, updated_at = NOW()\n' +
    '    ...',
  params: [ 5 ]
}
Database query error: {
  code: 'ER_BAD_FIELD_ERROR',
  message: "Unknown column 'target_table' in 'field list'",
  sql: '\n' +
    '          INSERT INTO admin_logs (action, target_table, target_id, admin_id, details)\n' +
    '          VAL...',
  params: [
    'restore_business',
    'service_providers',
    5,
    'admin',
    '{"action":"restore","businessId":5,"newStatus":"verified","newApplicationStatus":"approved"}'
  ]
}
